[tool:pytest]
# Pytest configuration for TurdParty
minversion = 6.0
addopts = 
    -ra
    --strict-markers
    --strict-config
    --cov=api
    --cov-report=term-missing
    --cov-report=html:test-results/htmlcov
    --cov-report=xml:test-results/coverage.xml
    --junit-xml=test-results/pytest-results.xml
    --html=test-results/pytest-report.html
    --self-contained-html
    -v
testpaths = 
    tests/unit
    tests/integration
python_files = test_*.py
python_classes = Test*
python_functions = test_*
markers =
    unit: Unit tests
    integration: Integration tests
    slow: Slow running tests
    api: API tests
    database: Database tests
    elk: ELK stack tests
    vm: Virtual machine tests
    file_injection: File injection tests
    security: Security tests
    performance: Performance tests
    smoke: Smoke tests
    regression: Regression tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
    ignore::UserWarning:urllib3.*
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S
log_file = test-results/pytest.log
log_file_level = DEBUG
log_file_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s (%(filename)s:%(lineno)d)
log_file_date_format = %Y-%m-%d %H:%M:%S
asyncio_mode = auto
timeout = 300
timeout_method = thread
