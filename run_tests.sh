#!/bin/bash

# TurdParty Comprehensive Test Runner
# Runs unit, integration, BDD, and E2E tests with proper reporting

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
TEST_RESULTS_DIR="test-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="${TEST_RESULTS_DIR}/test_run_${TIMESTAMP}.log"

# Create test results directory
mkdir -p "${TEST_RESULTS_DIR}"

# Logging function
log() {
    echo -e "${1}" | tee -a "${LOG_FILE}"
}

# Function to run command with logging
run_command() {
    local cmd="$1"
    local description="$2"
    
    log "${BLUE}Running: ${description}${NC}"
    log "Command: ${cmd}"
    
    if eval "${cmd}" 2>&1 | tee -a "${LOG_FILE}"; then
        log "${GREEN}✅ ${description} - PASSED${NC}"
        return 0
    else
        log "${RED}❌ ${description} - FAILED${NC}"
        return 1
    fi
}

# Function to check if service is running
check_service() {
    local service_name="$1"
    local url="$2"
    
    log "${YELLOW}Checking ${service_name}...${NC}"
    if curl -s "${url}" > /dev/null 2>&1; then
        log "${GREEN}✅ ${service_name} is running${NC}"
        return 0
    else
        log "${RED}❌ ${service_name} is not running${NC}"
        return 1
    fi
}

# Function to setup test environment
setup_test_environment() {
    log "${BLUE}Setting up test environment...${NC}"
    
    # Install dependencies if needed
    if [ ! -d "node_modules" ]; then
        log "Installing Node.js dependencies..."
        npm install
    fi
    
    # Install Python dependencies
    log "Installing Python dependencies..."
    pip install -r requirements.txt
    
    # Install Playwright browsers if needed
    if ! npx playwright --version > /dev/null 2>&1; then
        log "Installing Playwright browsers..."
        npx playwright install
    fi
    
    log "${GREEN}✅ Test environment setup complete${NC}"
}

# Function to run unit tests
run_unit_tests() {
    log "${BLUE}🧪 Running Unit Tests${NC}"
    
    run_command \
        "python -m pytest tests/unit -v --cov=api --cov-report=html:${TEST_RESULTS_DIR}/unit-coverage --cov-report=xml:${TEST_RESULTS_DIR}/unit-coverage.xml --junit-xml=${TEST_RESULTS_DIR}/unit-results.xml" \
        "Unit Tests"
}

# Function to run integration tests
run_integration_tests() {
    log "${BLUE}🔗 Running Integration Tests${NC}"
    
    # Check if services are running
    if ! check_service "API" "http://localhost:8000/health"; then
        log "${YELLOW}Starting services for integration tests...${NC}"
        docker-compose up -d
        sleep 30
    fi
    
    run_command \
        "python -m pytest tests/integration -v --junit-xml=${TEST_RESULTS_DIR}/integration-results.xml" \
        "Integration Tests"
}

# Function to run BDD tests
run_bdd_tests() {
    log "${BLUE}🥒 Running BDD Tests${NC}"
    
    # Check if services are running
    if ! check_service "API" "http://localhost:8000/health"; then
        log "${YELLOW}Starting services for BDD tests...${NC}"
        docker-compose up -d
        sleep 30
    fi
    
    run_command \
        "behave --format=pretty --format=json:${TEST_RESULTS_DIR}/behave-results.json --format=junit:${TEST_RESULTS_DIR}/behave-results.xml" \
        "BDD Tests"
}

# Function to run E2E tests
run_e2e_tests() {
    log "${BLUE}🎭 Running E2E Tests${NC}"
    
    # Check if services are running
    if ! check_service "API" "http://localhost:8000/health"; then
        log "${YELLOW}Starting services for E2E tests...${NC}"
        docker-compose up -d
        sleep 30
    fi
    
    run_command \
        "npx playwright test --reporter=html --reporter=junit --output-dir=${TEST_RESULTS_DIR}/playwright" \
        "E2E Tests"
}

# Function to generate test report
generate_test_report() {
    log "${BLUE}📊 Generating Test Report${NC}"
    
    local report_file="${TEST_RESULTS_DIR}/test_summary_${TIMESTAMP}.md"
    
    cat > "${report_file}" << EOF
# TurdParty Test Report

**Generated:** $(date)
**Test Run ID:** ${TIMESTAMP}

## Test Results Summary

### Unit Tests
- **Location:** tests/unit/
- **Coverage Report:** ${TEST_RESULTS_DIR}/unit-coverage/index.html
- **Results:** ${TEST_RESULTS_DIR}/unit-results.xml

### Integration Tests
- **Location:** tests/integration/
- **Results:** ${TEST_RESULTS_DIR}/integration-results.xml

### BDD Tests
- **Location:** features/
- **Results:** ${TEST_RESULTS_DIR}/behave-results.xml
- **JSON Report:** ${TEST_RESULTS_DIR}/behave-results.json

### E2E Tests
- **Location:** tests/e2e/
- **Results:** ${TEST_RESULTS_DIR}/playwright/
- **HTML Report:** ${TEST_RESULTS_DIR}/playwright/index.html

## Test Artifacts

All test artifacts are stored in: \`${TEST_RESULTS_DIR}/\`

## Logs

Full test execution log: \`${LOG_FILE}\`

EOF

    log "${GREEN}✅ Test report generated: ${report_file}${NC}"
}

# Function to cleanup
cleanup() {
    log "${BLUE}🧹 Cleaning up...${NC}"
    
    # Stop services if we started them
    if [ "${STARTED_SERVICES}" = "true" ]; then
        log "Stopping services..."
        docker-compose down
    fi
    
    log "${GREEN}✅ Cleanup complete${NC}"
}

# Main function
main() {
    local test_type="${1:-all}"
    
    log "${BLUE}🚀 TurdParty Test Runner${NC}"
    log "Test Type: ${test_type}"
    log "Timestamp: ${TIMESTAMP}"
    log "Log File: ${LOG_FILE}"
    log ""
    
    # Setup trap for cleanup
    trap cleanup EXIT
    
    # Setup test environment
    setup_test_environment
    
    local exit_code=0
    
    case "${test_type}" in
        "unit")
            run_unit_tests || exit_code=1
            ;;
        "integration")
            run_integration_tests || exit_code=1
            ;;
        "bdd"|"behave")
            run_bdd_tests || exit_code=1
            ;;
        "e2e"|"playwright")
            run_e2e_tests || exit_code=1
            ;;
        "all")
            run_unit_tests || exit_code=1
            run_integration_tests || exit_code=1
            run_bdd_tests || exit_code=1
            run_e2e_tests || exit_code=1
            ;;
        *)
            log "${RED}❌ Unknown test type: ${test_type}${NC}"
            log "Usage: $0 [unit|integration|bdd|e2e|all]"
            exit 1
            ;;
    esac
    
    # Generate test report
    generate_test_report
    
    if [ ${exit_code} -eq 0 ]; then
        log "${GREEN}🎉 All tests completed successfully!${NC}"
    else
        log "${RED}💥 Some tests failed. Check the logs for details.${NC}"
    fi
    
    exit ${exit_code}
}

# Run main function with all arguments
main "$@"
