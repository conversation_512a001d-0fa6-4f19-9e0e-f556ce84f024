#!/bin/bash

# Post-create script for TurdParty devcontainer
echo "🚀 Setting up TurdParty development environment..."

# Set proper permissions
sudo chown -R vscode:vscode /workspace

# Install Python dependencies
echo "📦 Installing Python dependencies..."
if [ -f "/workspace/requirements.txt" ]; then
    pip install -r /workspace/requirements.txt
fi

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
if [ -f "/workspace/frontend/package.json" ]; then
    cd /workspace/frontend && npm install
fi

# Set up pre-commit hooks
echo "🔧 Setting up pre-commit hooks..."
cd /workspace
if [ -f ".pre-commit-config.yaml" ]; then
    pre-commit install
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p /workspace/uploads
mkdir -p /workspace/logs
mkdir -p /workspace/data

# Set up git configuration if not already set
echo "🔧 Configuring git..."
if [ ! "$(git config --global user.name)" ]; then
    echo "⚠️  Git user.name not set. Please configure with:"
    echo "   git config --global user.name 'Your Name'"
fi

if [ ! "$(git config --global user.email)" ]; then
    echo "⚠️  Git user.email not set. Please configure with:"
    echo "   git config --global user.email '<EMAIL>'"
fi

# Create useful aliases
echo "🔧 Setting up development aliases..."
cat >> ~/.zshrc << 'EOF'

# TurdParty specific aliases
alias tp-start="docker-compose up -d"
alias tp-stop="docker-compose down"
alias tp-restart="docker-compose restart"
alias tp-logs="docker-compose logs -f"
alias tp-api="docker-compose logs -f api"
alias tp-elk="docker-compose logs -f elasticsearch logstash kibana"
alias tp-test="python -m pytest"
alias tp-format="black . && isort ."
alias tp-lint="flake8 . && pylint api/"

# Development shortcuts
alias api-shell="docker exec -it turdpartycollab_api /bin/bash"
alias es-health="curl -s http://localhost:9200/_cluster/health | jq"
alias kibana-open="echo 'Opening Kibana at http://localhost:5601'"

# Quick navigation
alias cdapi="cd /workspace/api"
alias cdfrontend="cd /workspace/frontend"
alias cdconfig="cd /workspace/config"
alias cddocker="cd /workspace/docker"
EOF

# Set up development environment file
echo "📝 Setting up environment file..."
if [ ! -f "/workspace/.env" ]; then
    cp /workspace/.env.example /workspace/.env
    echo "✅ Created .env file from .env.example"
fi

# Create a development script
echo "📝 Creating development helper script..."
cat > /workspace/dev.sh << 'EOF'
#!/bin/bash

# TurdParty Development Helper Script

case "$1" in
    start)
        echo "🚀 Starting TurdParty development environment..."
        docker-compose up -d
        echo "✅ Services started!"
        echo "📊 API: http://localhost:8000"
        echo "📊 API Docs: http://localhost:8000/docs"
        echo "📊 Kibana: http://localhost:5601"
        echo "📊 Elasticsearch: http://localhost:9200"
        ;;
    stop)
        echo "🛑 Stopping TurdParty development environment..."
        docker-compose down
        echo "✅ Services stopped!"
        ;;
    restart)
        echo "🔄 Restarting TurdParty development environment..."
        docker-compose restart
        echo "✅ Services restarted!"
        ;;
    logs)
        echo "📋 Showing logs for all services..."
        docker-compose logs -f
        ;;
    test)
        echo "🧪 Running tests..."
        python -m pytest -v
        ;;
    format)
        echo "🎨 Formatting code..."
        black .
        isort .
        echo "✅ Code formatted!"
        ;;
    lint)
        echo "🔍 Linting code..."
        flake8 .
        pylint api/
        ;;
    health)
        echo "🏥 Checking service health..."
        echo "API Health:"
        curl -s http://localhost:8000/health | jq || echo "API not responding"
        echo -e "\nElasticsearch Health:"
        curl -s http://localhost:9200/_cluster/health | jq || echo "Elasticsearch not responding"
        ;;
    inject)
        if [ -z "$2" ]; then
            echo "Usage: $0 inject <file_path>"
            exit 1
        fi
        echo "💉 Injecting file: $2"
        curl -X POST "http://localhost:8000/api/v1/file_injection/" \
            -H "Content-Type: multipart/form-data" \
            -F "file=@$2" \
            -F "target_path=/app/injected/$(basename $2)" \
            -F "permissions=0755" \
            -F "description=Development test injection"
        ;;
    *)
        echo "TurdParty Development Helper"
        echo "Usage: $0 {start|stop|restart|logs|test|format|lint|health|inject <file>}"
        echo ""
        echo "Commands:"
        echo "  start    - Start all services"
        echo "  stop     - Stop all services"
        echo "  restart  - Restart all services"
        echo "  logs     - Show logs for all services"
        echo "  test     - Run tests"
        echo "  format   - Format code with black and isort"
        echo "  lint     - Lint code with flake8 and pylint"
        echo "  health   - Check service health"
        echo "  inject   - Inject a file (usage: inject <file_path>)"
        ;;
esac
EOF

chmod +x /workspace/dev.sh

# Create a sample test file for injection
echo "📝 Creating sample test file..."
cat > /workspace/test_injection.sh << 'EOF'
#!/bin/bash
echo "Hello from TurdParty file injection!"
echo "This is a test script injected via the API"
echo "Current time: $(date)"
echo "Current user: $(whoami)"
echo "Current directory: $(pwd)"
EOF

chmod +x /workspace/test_injection.sh

echo "✅ TurdParty development environment setup complete!"
echo ""
echo "🎯 Quick start:"
echo "   ./dev.sh start     # Start all services"
echo "   ./dev.sh health    # Check service health"
echo "   ./dev.sh inject test_injection.sh  # Test file injection"
echo ""
echo "📚 Useful commands:"
echo "   tp-start          # Start services"
echo "   tp-logs           # View logs"
echo "   tp-test           # Run tests"
echo "   tp-format         # Format code"
echo ""
echo "🌐 Access points:"
echo "   API: http://localhost:8000"
echo "   API Docs: http://localhost:8000/docs"
echo "   Kibana: http://localhost:5601"
echo "   Elasticsearch: http://localhost:9200"
