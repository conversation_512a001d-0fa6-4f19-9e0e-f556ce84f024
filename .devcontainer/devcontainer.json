{
  "name": "TurdParty Development Environment",
  "dockerComposeFile": [
    "../docker-compose.yml",
    "docker-compose.dev.yml"
  ],
  "service": "devcontainer",
  "workspaceFolder": "/workspace",
  "shutdownAction": "stopCompose",
  
  // Features to install
  "features": {
    "ghcr.io/devcontainers/features/docker-in-docker:2": {
      "version": "latest",
      "enableNonRootDocker": "true",
      "moby": "true"
    },
    "ghcr.io/devcontainers/features/git:1": {
      "version": "latest",
      "ppa": "false"
    },
    "ghcr.io/devcontainers/features/github-cli:1": {
      "version": "latest"
    },
    "ghcr.io/devcontainers/features/node:1": {
      "version": "18",
      "nodeGypDependencies": "true"
    },
    "ghcr.io/devcontainers/features/python:1": {
      "version": "3.10",
      "installTools": "true"
    }
  },

  // Configure tool-specific properties
  "customizations": {
    "vscode": {
      "extensions": [
        // Python extensions
        "ms-python.python",
        "ms-python.ruff",
        "ms-python.mypy-type-checker",
        
        // Docker extensions
        "ms-azuretools.vscode-docker",
        "ms-vscode-remote.remote-containers",
        
        // API development
        "humao.rest-client",
        "42crunch.vscode-openapi",
        
        // Frontend development
        "bradlc.vscode-tailwindcss",
        "esbenp.prettier-vscode",
        "ms-vscode.vscode-typescript-next",
        
        // Git and collaboration
        "eamodio.gitlens",
        "github.vscode-pull-request-github",
        
        // Utilities
        "ms-vscode.vscode-json",
        "redhat.vscode-yaml",
        "ms-vscode.hexdump",
        "streetsidesoftware.code-spell-checker"
      ],
      "settings": {
        "python.defaultInterpreterPath": "/usr/local/bin/python",
        "python.linting.enabled": false,
        "[python]": {
          "editor.formatOnSave": true,
          "editor.codeActionsOnSave": {
            "source.fixAll.ruff": "explicit",
            "source.organizeImports.ruff": "explicit"
          },
          "editor.defaultFormatter": "ms-python.ruff"
        },
        "ruff.enable": true,
        "ruff.organizeImports": true,
        "mypy-type-checker.importStrategy": "fromEnvironment",
        "editor.formatOnSave": true,
        "files.exclude": {
          "**/__pycache__": true,
          "**/.pytest_cache": true,
          "**/node_modules": true,
          "**/.git": false
        },
        "docker.dockerPath": "/usr/local/bin/docker",
        "terminal.integrated.defaultProfile.linux": "zsh"
      }
    }
  },

  // Use 'forwardPorts' to make a list of ports inside the container available locally
  "forwardPorts": [
    8000,  // API
    3000,  // Frontend
    5601,  // Kibana
    9200,  // Elasticsearch
    5000   // Logstash
  ],

  // Port attributes
  "portsAttributes": {
    "8000": {
      "label": "TurdParty API",
      "onAutoForward": "notify"
    },
    "3000": {
      "label": "Frontend",
      "onAutoForward": "silent"
    },
    "5601": {
      "label": "Kibana",
      "onAutoForward": "silent"
    },
    "9200": {
      "label": "Elasticsearch",
      "onAutoForward": "silent"
    },
    "5000": {
      "label": "Logstash",
      "onAutoForward": "silent"
    }
  },

  // Use 'postCreateCommand' to run commands after the container is created
  "postCreateCommand": ".devcontainer/post-create.sh",

  // Use 'postStartCommand' to run commands after the container starts
  "postStartCommand": ".devcontainer/post-start.sh",

  // Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root
  "remoteUser": "vscode",

  // Environment variables
  "containerEnv": {
    "PYTHONPATH": "/workspace",
    "DOCKER_BUILDKIT": "1",
    "COMPOSE_DOCKER_CLI_BUILD": "1"
  },

  // Mount the Docker socket for Docker-in-Docker
  "mounts": [
    "source=/var/run/docker.sock,target=/var/run/docker-host.sock,type=bind"
  ]
}
