# TurdParty Development Container

This directory contains the development container configuration for TurdParty, providing a consistent development environment with Docker-in-Docker support.

## Features

### 🐳 **Docker-in-Docker Support**
- Full Docker and Docker Compose functionality within the container
- Ability to build and run the TurdParty services from within the devcontainer
- Isolated development environment

### 🛠️ **Development Tools**
- **Python 3.10** with development packages
- **Node.js 18** for frontend development
- **Git** with GitHub CLI integration
- **VS Code extensions** pre-configured for Python, Docker, and API development
- **Pre-commit hooks** for code quality
- **Zsh** with Oh My Zsh for enhanced terminal experience

### 📦 **Pre-installed Packages**
- **Python**: black, flake8, isort, pylint, mypy, pytest, ipython
- **Node.js**: TypeScript, Prettier, ESLint
- **System**: curl, wget, jq, tree, htop, vim, nano

## Quick Start

### 1. Open in VS Code
```bash
# Clone the repository
git clone <repository-url>
cd turdparty-collab

# Open in VS Code
code .

# When prompted, click "Reopen in Container"
# Or use Command Palette: "Dev Containers: Reopen in Container"
```

### 2. Start Development Services
```bash
# Use the helper script
./dev.sh start

# Or use docker-compose directly
docker-compose up -d
```

### 3. Verify Setup
```bash
# Check service health
./dev.sh health

# View logs
./dev.sh logs
```

## Development Workflow

### 🚀 **Starting Development**
```bash
# Start all services
./dev.sh start

# Check health
./dev.sh health

# View API documentation
# Open http://localhost:8000/docs
```

### 🧪 **Testing**
```bash
# Run all tests
./dev.sh test

# Or use pytest directly
python -m pytest -v

# Test file injection
./dev.sh inject test_injection.sh
```

### 🎨 **Code Quality**
```bash
# Format code
./dev.sh format

# Lint code
./dev.sh lint

# Run pre-commit hooks
pre-commit run --all-files
```

## Available Commands

### Helper Script (`./dev.sh`)
- `start` - Start all services
- `stop` - Stop all services  
- `restart` - Restart all services
- `logs` - Show logs for all services
- `test` - Run tests
- `format` - Format code with black and isort
- `lint` - Lint code with flake8 and pylint
- `health` - Check service health
- `inject <file>` - Test file injection

### Zsh Aliases
- `tp-start` - Start services
- `tp-stop` - Stop services
- `tp-restart` - Restart services
- `tp-logs` - View logs
- `tp-api` - View API logs
- `tp-elk` - View ELK stack logs
- `tp-test` - Run tests
- `tp-format` - Format code
- `tp-lint` - Lint code

### Navigation Aliases
- `cdapi` - Navigate to API directory
- `cdfrontend` - Navigate to frontend directory
- `cdconfig` - Navigate to config directory
- `cddocker` - Navigate to docker directory

## Service Access

When services are running, you can access:

- **API**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Kibana**: http://localhost:5601
- **Elasticsearch**: http://localhost:9200
- **Logstash**: http://localhost:5000

## File Structure

```
.devcontainer/
├── devcontainer.json       # Main devcontainer configuration
├── docker-compose.dev.yml  # Development services
├── Dockerfile.dev          # Development container image
├── post-create.sh          # Setup script (runs once)
├── post-start.sh           # Startup script (runs each time)
└── README.md              # This file
```

## Customisation

### Adding VS Code Extensions
Edit `.devcontainer/devcontainer.json`:
```json
"customizations": {
  "vscode": {
    "extensions": [
      "your.extension.id"
    ]
  }
}
```

### Adding System Packages
Edit `.devcontainer/Dockerfile.dev`:
```dockerfile
RUN apt-get update && apt-get install -y \
    your-package-name
```

### Adding Python Packages
Edit `requirements.txt` or install directly:
```bash
pip install your-package
```

## Troubleshooting

### Docker Issues
```bash
# Check Docker daemon
docker info

# Restart Docker service
sudo service docker restart

# Check container status
docker ps -a
```

### Permission Issues
```bash
# Fix workspace permissions
sudo chown -R vscode:vscode /workspace

# Fix Docker socket permissions
sudo chmod 666 /var/run/docker.sock
```

### Service Issues
```bash
# Check service logs
docker-compose logs <service-name>

# Restart specific service
docker-compose restart <service-name>

# Rebuild services
docker-compose build --no-cache
```

### Port Conflicts
If ports are already in use:
1. Stop conflicting services
2. Or modify ports in `docker-compose.yml`
3. Update port forwarding in `devcontainer.json`

## Best Practices

### 🔒 **Security**
- Never commit sensitive data to the repository
- Use environment variables for configuration
- Keep the `.env` file out of version control

### 📝 **Code Quality**
- Run pre-commit hooks before committing
- Write tests for new functionality
- Follow Python PEP 8 style guidelines
- Use type hints where appropriate

### 🐳 **Docker**
- Use multi-stage builds for production images
- Keep images small and focused
- Use `.dockerignore` to exclude unnecessary files
- Tag images appropriately

### 📊 **Monitoring**
- Check service health regularly
- Monitor logs for errors
- Use Kibana for log analysis
- Set up alerts for critical issues

## Contributing

1. Create a feature branch
2. Make your changes
3. Run tests and linting
4. Commit with conventional commit messages
5. Create a pull request

For more information, see the main project README.
