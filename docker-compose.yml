version: '3.8'

services:
  api:
    container_name: turdpartycollab_api
    build:
      context: .
      dockerfile: docker/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=********************************************/turdparty
      - DATABASE_URL_ASYNC=postgresql+asyncpg://postgres:postgres@postgres:5432/turdparty
      - PYTHONPATH=/app
      - DEBUG=true
      - TEST_MODE=true
      - MINIO_HOST=minio
      - MINIO_PORT=9000
      - MINIO_ACCESS_KEY=minioadmin
      - MINIO_SECRET_KEY=minioadmin
      - MINIO_DIRECT=true
      - FILE_UPLOAD_DIR=/app/uploads
      - API_PREFIX=/api/v1
    volumes:
      - ./api:/app/api
      - ./scripts:/app/scripts
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - postgres
      - minio
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  frontend:
    container_name: turdpartycollab_frontend
    build:
      context: .
      dockerfile: docker/Dockerfile.frontend
    ports:
      - "3000:3000"
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - NODE_ENV=development
      - CHOKIDAR_USEPOLLING=true
      - WDS_SOCKET_PORT=3000
    volumes:
      - ./frontend:/app
      - frontend_node_modules:/app/node_modules
    depends_on:
      - api
    networks:
      - turdpartycollab_net
      - traefik_network
    restart: unless-stopped

  postgres:
    container_name: turdpartycollab_postgres
    image: postgres:14-alpine
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
      - POSTGRES_DB=turdparty
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./db/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - turdpartycollab_net
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  minio:
    container_name: turdpartycollab_minio
    image: minio/minio:latest
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      - MINIO_ROOT_USER=minioadmin
      - MINIO_ROOT_PASSWORD=minioadmin
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    networks:
      - turdpartycollab_net
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

volumes:
  postgres_data:
    name: turdpartycollab_postgres_data
  minio_data:
    name: turdpartycollab_minio_data
  frontend_node_modules:
    name: turdpartycollab_frontend_node_modules

networks:
  turdpartycollab_net:
    name: turdpartycollab_net
    driver: bridge
  traefik_network:
    name: traefik_network
    external: true
