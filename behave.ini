[behave]
# Behave configuration for TurdParty BDD tests

# Paths
paths = features

# Output format
format = pretty
outfiles = test-results/behave-report.txt

# Additional formatters
formatters = 
    pretty
    json:test-results/behave-results.json
    html:test-results/behave-report.html
    junit:test-results/behave-results.xml

# Logging
logging_level = INFO
logging_format = %(asctime)s - %(name)s - %(levelname)s - %(message)s
logging_datefmt = %Y-%m-%d %H:%M:%S

# Show skipped scenarios
show_skipped = true

# Show multiline arguments
show_multiline = true

# Show source location
show_source = true

# Show timings
show_timings = true

# Stop on first failure
stop = false

# Tags to include/exclude
# tags = ~@skip,~@wip

# Default tags
default_tags = 

# Summary
summary = true

# Quiet mode
quiet = false

# Verbose mode
verbose = false

# Color output
color = true

# Include scenario outline examples in junit output
junit_directory = test-results/behave-junit

# Stage definitions
stage_definitions = 
    features/steps

# User data
userdata_defines = 
    api_base_url=http://localhost:8000
    elasticsearch_url=http://localhost:9200
    kibana_url=http://localhost:5601
    timeout=30
